import { NextRequest, NextResponse } from 'next/server';
import { loadTemplates, loadTemplateFile, replacePlaceholders, generatePDFStyles } from '@/lib/templates';
import { replaceApplicantPhoto } from '@/lib/template-utils';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { templateId, data, photoPath, addQRCode, qrCodeText } = body;

    // Validate required fields
    if (!templateId || !data) {
      return NextResponse.json(
        { error: 'Template ID and data are required' },
        { status: 400 }
      );
    }

    // Load and validate template
    const templates = await loadTemplates();
    const template = templates.find(t => t.id === templateId);

    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Process template content
    const templateContent = await loadTemplateFile(template.filename);
    let processedContent = replacePlaceholders(templateContent, data);

    // Handle photo replacement
    if (photoPath) {
      processedContent = replaceApplicantPhoto(processedContent, photoPath);
    }

    // Add QR code if requested
    if (addQRCode && qrCodeText) {
      const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=80x80&data=${encodeURIComponent(qrCodeText)}`;

      const qrCodeHtml = `
        <div style="position: absolute; bottom: 20px; left: 20px; z-index: 1000;">
          <div style="background: white; padding: 10px; border: 1px solid #ccc; border-radius: 5px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <img src="${qrCodeUrl}" alt="QR Code" style="width: 40px; height: 40px; margin-bottom: 5px; display: block;" />
            <div style="font-size: 12px; color: #666; font-family: Arial, sans-serif;">${qrCodeText}</div>
          </div>
        </div>
      `;

      processedContent = processedContent.replace('</body>', `${qrCodeHtml}</body>`);
    }

    // Apply PDF styles
    const pdfStyles = generatePDFStyles(template.layoutSize);
    const htmlWithStyles = processedContent.replace('</head>', `${pdfStyles}</head>`);

    return NextResponse.json({
      success: true,
      htmlContent: htmlWithStyles,
      templateName: template.name,
      layoutSize: template.layoutSize
    });
  } catch (error) {
    console.error('Error generating document:', error);
    return NextResponse.json(
      { error: 'Failed to generate document' },
      { status: 500 }
    );
  }
}