"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import {
  AlertCircle,
  ArrowLeft,
  Bell,
  Shield,
  Edit,
  CheckCircle,
  Printer,
} from "lucide-react";
import { useNotifications } from "@/contexts/notification-context";
import type { Notification } from "@/contexts/notification-context";
import { PDFImageViewer } from "@/components/pdf-image-viewer";

export default function NotificationDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { notifications, markAsRead, isAdminMode } = useNotifications();
  const [notification, setNotification] = useState<Notification | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isHydrated, setIsHydrated] = useState(false);
  const [isApproved, setIsApproved] = useState(false);
  const [isProcessingApproval, setIsProcessingApproval] = useState(false);

  useEffect(() => {
    const checkAdminAndLoadNotification = async () => {
      // Set hydrated to true on client side
      setIsHydrated(true);
      setIsLoading(false);

      // Check if user has admin access
      if (!isAdminMode) {
        return;
      }

      const notificationId = params.id as string;

      // Try to find in local state first
      let foundNotification = notifications.find(
        (n) => n.id === notificationId
      );

      // If not found locally, fetch from database
      if (!foundNotification) {
        try {
          const response = await fetch(`/api/notifications/${notificationId}`);
          const data = await response.json();
          if (data.success) {
            foundNotification = {
              ...data.notification,
              createdAt: new Date(data.notification.createdAt),
            };
          }
        } catch (error) {
          console.error("Error fetching notification:", error);
        }
      }

      if (foundNotification) {
        setNotification(foundNotification);
        // Mark as read when viewing details
        if (!foundNotification.isRead) {
          markAsRead(foundNotification.id);
        }
      }
    };

    checkAdminAndLoadNotification();
  }, [params.id, notifications, markAsRead, isAdminMode]);

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  const handleEdit = () => {
    if (notification?.pdfData?.templateId) {
      // Redirect to template form page with edit mode and notification ID
      const templateId = notification.pdfData.templateId;
      const queryParams = new URLSearchParams();
      queryParams.append("edit", "true");
      queryParams.append("notificationId", notification.id);

      // Navigate to the template form page - data will be fetched from notification API
      router.push(`/templates/form/${templateId}?${queryParams.toString()}`);
    }
  };

  const handleApproved = async () => {
    if (!notification?.pdfData) {
      toast.error("No PDF data available for approval");
      return;
    }

    setIsProcessingApproval(true);

    try {
      // Get current date
      const now = new Date();
      const day = now.getDate().toString();
      const month = now.toLocaleString("default", { month: "long" });
      const year = now.getFullYear().toString();

      // Fill up date fields in the user data
      const updatedUserData = { ...notification.pdfData.userData };

      // Find and fill date-related fields
      Object.keys(updatedUserData).forEach((key) => {
        const lowerKey = key.toLowerCase();
        if (lowerKey.includes("day") && !lowerKey.includes("birthday")) {
          updatedUserData[key] = day;
        } else if (lowerKey.includes("month") && !lowerKey.includes("birth")) {
          updatedUserData[key] = month;
        } else if (lowerKey.includes("year") && !lowerKey.includes("birth")) {
          updatedUserData[key] = year;
        }
      });

      // Create updated PDF data with filled dates
      const updatedPdfData = {
        ...notification.pdfData,
        userData: updatedUserData,
        approvedAt: now.toISOString(),
      };

      // Generate new PDF with QR code and filled dates
      const response = await fetch("/api/templates/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          templateId: notification.pdfData.templateId,
          data: updatedUserData,
          photoPath: null, // Will handle photo replacement manually
          addQRCode: true, // Flag to add QR code
          qrCodeText: "Validated", // QR code content
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to generate approved document");
      }

      const result = await response.json();

      // Generate PDF with the updated HTML content (including QR code)
      const { jsPDF } = await import("jspdf");
      const html2canvas = (await import("html2canvas")).default;

      // Create an iframe to render the HTML content
      const iframe = document.createElement("iframe");
      iframe.style.position = "absolute";
      iframe.style.left = "-9999px";
      iframe.style.width = "794px";
      iframe.style.height = "1123px";
      document.body.appendChild(iframe);

      const iframeDoc =
        iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) {
        throw new Error("Could not access iframe document");
      }

      iframeDoc.open();
      iframeDoc.write(result.htmlContent);
      iframeDoc.close();

      // Handle existing photo replacement if needed
      if (notification.pdfData.photoBase64) {
        const photoSelectors = [
          'img[data-placeholder="applicant-photo"]',
          'img[src*="applicant-photo"]',
          'img[alt*="applicant"]',
          'img[alt*="photo"]',
          'img[src*="placeholder"]',
          'img[src*="default"]',
        ];

        const photoElements = iframeDoc.querySelectorAll(
          photoSelectors.join(", ")
        );
        photoElements.forEach((img) => {
          (img as HTMLImageElement).src = notification.pdfData!.photoBase64!;
        });
      }

      // Wait for content and QR code to render (reduced timeout)
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Convert to canvas and generate PDF
      const canvas = await html2canvas(iframeDoc.body, {
        scale: 1,
        useCORS: true,
        allowTaint: true,
        backgroundColor: "#ffffff",
        width: 794,
        height: 1123,
      });

      const imgData = canvas.toDataURL("image/jpeg", 0.8);
      const pdf = new jsPDF(
        "p",
        "pt",
        result.layoutSize === "Letter" ? "letter" : "a4"
      );
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();

      pdf.addImage(imgData, "JPEG", 0, 0, pdfWidth, pdfHeight);

      // Create updated embedded data
      const embeddedData = {
        ...updatedPdfData,
        photoBase64: notification.pdfData.photoBase64,
        generatedAt: new Date().toISOString(),
        layoutSize: result.layoutSize,
      };

      // Add embedded data as invisible text
      pdf.setTextColor(255, 255, 255);
      pdf.setFontSize(1);
      pdf.text(
        `LDIS_DATA_BEGIN:${JSON.stringify(embeddedData)}:LDIS_DATA_END`,
        1,
        1
      );

      // Convert PDF to blob and upload
      const pdfBlob = pdf.output("blob");
      const filename = `${notification.title} (Approved).pdf`;
      const pdfFile = new File([pdfBlob], filename, {
        type: "application/pdf",
      });

      // Upload the approved PDF
      const uploadFormData = new FormData();
      uploadFormData.append("file", pdfFile);
      if (notification.pdfFilePath) {
        uploadFormData.append("oldFilePath", notification.pdfFilePath);
      }

      const uploadResponse = await fetch("/api/pdf/parse", {
        method: "POST",
        body: uploadFormData,
      });

      if (!uploadResponse.ok) {
        throw new Error("Failed to upload approved PDF");
      }

      const uploadResult = await uploadResponse.json();

      // Update the notification with approved PDF
      const updateResponse = await fetch(
        `/api/notifications/${notification.id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            title: filename,
            message: `Document approved at ${new Date().toLocaleTimeString()}`,
            pdfFileName: uploadResult.fileName,
            pdfFilePath: uploadResult.savedFile,
            pdfData: embeddedData,
          }),
        }
      );

      if (!updateResponse.ok) {
        throw new Error("Failed to update notification");
      }

      // Clean up iframe
      document.body.removeChild(iframe);

      // Refresh the notification data
      const refreshResponse = await fetch(`/api/notifications/${params.id}`);
      const refreshData = await refreshResponse.json();
      if (refreshData.success) {
        setNotification(refreshData.notification);
      }

      setIsApproved(true);
      toast.success("Document approved successfully!");
    } catch (error) {
      console.error("Error approving document:", error);
      toast.error("Failed to approve document");
    } finally {
      setIsProcessingApproval(false);
    }
  };

  const handlePrint = () => {
    // TODO: Implement print functionality
    console.log("Print button clicked for notification:", notification?.id);
  };

  // Show loading state while checking admin mode or during SSR
  if (isLoading || !isHydrated) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show access denied if not in admin mode
  if (!isAdminMode) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="text-center py-12">
          <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Admin Access Required</h3>
          <p className="text-muted-foreground mb-6">
            You need to enable admin mode to view notifications.
          </p>
          <Button onClick={() => router.push("/settings")}>
            <Shield className="h-4 w-4 mr-2" />
            Go to Settings
          </Button>
        </div>
      </div>
    );
  }

  if (!notification) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              Notification Not Found
            </h3>
            <p className="text-muted-foreground mb-6">
              The notification you&apos;re looking for doesn&apos;t exist or has
              been removed.
            </p>
            <Button onClick={() => router.push("/")}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Fixed Sidebar - blank */}
      <div className="fixed left-0 top-0 w-64 h-screen bg-muted/30 border-r z-40">
        <div className="p-6">
          <div className="flex items-center gap-3 mb-6">
            <Bell className="h-6 w-6 text-primary" />
            <h2 className="text-lg font-semibold">Notification Details</h2>
          </div>

          {/* Empty sidebar content */}
        </div>
      </div>

      {/* Main Content */}
      <div className="ml-64 flex h-[calc(100vh-120px)]">
        {/* PDF Preview Area */}
        <div className="flex-1 flex flex-col">
          {notification.pdfUrl ? (
            <PDFImageViewer
              pdfUrl={`${notification.pdfUrl}?t=${Date.now()}`}
              fileName={notification.pdfFileName}
              className="h-full border-r"
            />
          ) : (
            <div className="flex-1 flex items-center justify-center bg-background">
              <div className="text-center">
                <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No PDF Available</h3>
                <p className="text-muted-foreground">
                  This notification doesn't have an associated PDF file.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Right Panel - Template Details */}
        <div className="w-80 bg-background flex flex-col overflow-hidden border-l">
          <div className="p-4 border-b">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold">Template Details</h2>
            </div>
            {/* Action Buttons */}
            <div className="flex gap-2 mb-2">
              <Button
                onClick={handleEdit}
                disabled={!notification?.pdfData?.templateId}
                className="flex-1"
                variant="outline"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button
                onClick={handleApproved}
                disabled={isProcessingApproval || isApproved}
                className="flex-1"
                variant="default"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                {isProcessingApproval
                  ? "Processing..."
                  : isApproved
                  ? "Approved"
                  : "Approve"}
              </Button>
            </div>

            {/* Print Button - Show after approval */}
            {isApproved && (
              <Button
                onClick={handlePrint}
                className="w-full"
                variant="secondary"
              >
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
            )}
          </div>
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Notification Information */}
              <div>
                <h3 className="text-sm font-semibold mb-3">
                  Notification Information
                </h3>
                <div className="space-y-3">
                  <div>
                    <Label className="text-xs font-medium text-muted-foreground">
                      Title
                    </Label>
                    <p className="text-xs bg-muted/50 p-2 rounded">
                      {notification.title}
                    </p>
                  </div>
                  {/* Message and file name hidden per user request */}
                  <div>
                    <Label className="text-xs font-medium text-muted-foreground">
                      Uploaded At
                    </Label>
                    <p className="text-xs bg-muted/50 p-2 rounded">
                      {formatDate(notification.createdAt.toString())}
                    </p>
                  </div>
                </div>
              </div>

              {/* Template Information */}
              {notification.pdfData && (
                <div className="space-y-6">
                  {/* Template Information */}
                  <div>
                    <h3 className="text-sm font-semibold mb-3">
                      Template Information
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <Label className="text-xs font-medium text-muted-foreground">
                          Template ID
                        </Label>
                        <p className="text-xs font-mono bg-muted/50 p-2 rounded">
                          {notification.pdfData.templateId}
                        </p>
                      </div>
                      <div>
                        <Label className="text-xs font-medium text-muted-foreground">
                          Template Name
                        </Label>
                        <p className="text-xs bg-muted/50 p-2 rounded">
                          {notification.pdfData.templateName}
                        </p>
                      </div>
                      <div>
                        <Label className="text-xs font-medium text-muted-foreground">
                          Layout Size
                        </Label>
                        <p className="text-xs bg-muted/50 p-2 rounded">
                          {notification.pdfData.layoutSize}
                        </p>
                      </div>
                      <div>
                        <Label className="text-xs font-medium text-muted-foreground">
                          Generated At
                        </Label>
                        <p className="text-xs bg-muted/50 p-2 rounded">
                          {formatDate(notification.pdfData.generatedAt)}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* User Data */}
                  <div>
                    <h3 className="text-sm font-semibold mb-3">User Data</h3>
                    <div className="space-y-2">
                      {Object.entries(notification.pdfData.userData).map(
                        ([key, value]) => (
                          <div key={key}>
                            <Label className="text-xs font-medium text-muted-foreground">
                              {key}
                            </Label>
                            <p className="text-xs bg-muted/50 p-2 rounded">
                              {value || (
                                <span className="italic text-muted-foreground">
                                  Empty
                                </span>
                              )}
                            </p>
                          </div>
                        )
                      )}
                    </div>
                  </div>

                  {/* Embedded Photo */}
                  {notification.pdfData.photoBase64 && (
                    <div>
                      <h3 className="text-sm font-semibold mb-3">
                        Uploaded photo
                      </h3>
                      <div className="w-full">
                        <img
                          src={notification.pdfData.photoBase64}
                          alt="Embedded photo"
                          className="w-full h-auto border rounded-lg"
                        />
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
