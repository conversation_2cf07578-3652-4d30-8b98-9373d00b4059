import { NextRequest, NextResponse } from 'next/server';
import { NotificationModel } from '@/lib/models/notification';
import fs from 'fs';
import path from 'path';

// GET /api/notifications/[id]/pdf - Serve the PDF file for a notification
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const notification = await NotificationModel.findById(id);
    
    if (!notification) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      );
    }
    
    if (!notification.pdfFilePath) {
      return NextResponse.json(
        { success: false, error: 'No PDF file associated with this notification' },
        { status: 404 }
      );
    }
    
    // Construct the full path to the PDF file
    const fullPath = path.join(process.cwd(), 'public', notification.pdfFilePath);
    
    // Check if file exists
    if (!fs.existsSync(fullPath)) {
      return NextResponse.json(
        { success: false, error: 'PDF file not found on disk' },
        { status: 404 }
      );
    }
    
    // Read the file
    const fileBuffer = fs.readFileSync(fullPath);
    
    // Return the PDF file with proper headers
    return new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `inline; filename="${notification.pdfFileName || 'document.pdf'}"`,
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'X-Frame-Options': 'SAMEORIGIN', // Allow iframe from same origin
        'Access-Control-Allow-Origin': '*', // Allow CORS
        'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  } catch (error) {
    console.error('Error serving PDF file:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to serve PDF file' },
      { status: 500 }
    );
  }
}

// OPTIONS /api/notifications/[id]/pdf - Handle CORS preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}

// HEAD /api/notifications/[id]/pdf - Handle HEAD requests for PDF validation
export async function HEAD(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const notification = await NotificationModel.findById(id);

    if (!notification || !notification.pdfFilePath) {
      return new NextResponse(null, { status: 404 });
    }

    // Construct the full path to the PDF file
    const fullPath = path.join(process.cwd(), 'public', notification.pdfFilePath);

    // Check if file exists
    if (!fs.existsSync(fullPath)) {
      return new NextResponse(null, { status: 404 });
    }

    // Return headers without body
    return new NextResponse(null, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `inline; filename="${notification.pdfFileName || 'document.pdf'}"`,
        'Cache-Control': 'public, max-age=3600',
        'X-Frame-Options': 'SAMEORIGIN',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  } catch (error) {
    console.error('Error handling HEAD request for PDF file:', error);
    return new NextResponse(null, { status: 500 });
  }
}
